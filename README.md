# Extra Stores Data Warehouse Terraform Infrastructure

This repository contains Terraform configuration for provisioning and managing the data warehouse infrastructure for Extra Stores on Google Cloud Platform (GCP).

## Overview

This Terraform configuration sets up a complete data lake and analytics infrastructure including:

- **Compute Engine VM** for data processing workloads
- **Datastream** for real-time data replication
- **BigQuery** for data warehousing and analytics
- **IAM roles and permissions** for data lake users
- **Required GCP APIs** enablement
- **Service accounts** with appropriate permissions

## Architecture

The infrastructure includes the following components:

### Compute Resources
- **VM Instance**: `vm-datalake-endur` (n2-standard-4) running Debian 12
- **Network**: Connected to shared VPC (`ex-datalake-prod-prod-vpc`)
- **Service Account**: Custom service account with logging and monitoring permissions

### Data Services
- **Datastream**: Private connection for real-time data replication
- **BigQuery**: Data warehouse for analytics (managed via IAM permissions)
- **Cloud Storage**: Object storage for data lake (managed via IAM permissions)

### Security & Access
- **IAM Bindings**: Comprehensive role assignments for data lake users
- **Service Accounts**: Dedicated service accounts for different services
- **Secret Manager**: For secure credential storage

## Prerequisites

Before using this Terraform configuration, ensure you have:

1. **Terraform** installed (version 0.12+)
2. **Google Cloud SDK** installed and configured
3. **GCP Project** with billing enabled
4. **Terraform state bucket** (`ext-datalake-tf-state`) already created
5. **Network infrastructure** (VPC and subnets) already provisioned
6. **Appropriate GCP permissions** to create resources

## Configuration

### Variables

The configuration uses the following variables:

| Variable | Type | Description | Default |
|----------|------|-------------|---------|
| `project` | string | The GCP project ID | - |
| `region` | string | The GCP region | - |
| `datalake_users` | list(string) | List of users with data lake access | See variables.tf |

### Default Values

Current configuration is set for:
- **Project**: `ex-datalake-prod-prod`
- **Region**: `me-central2`
- **Data Lake Users**: 7 users from extrastores.com domain

## Usage

### 1. Clone the Repository
```bash
git clone <repository-url>
cd extra-dwh-terraform
```

### 2. Initialize Terraform
```bash
terraform init
```

### 3. Review the Plan
```bash
terraform plan
```

### 4. Apply the Configuration
```bash
terraform apply
```

### 5. Verify Resources
After successful deployment, verify that all resources are created in the GCP Console.

## File Structure

```
.
├── README.md              # This file
├── api.tf                 # GCP API enablement
├── backend.tf             # Terraform backend configuration
├── compute-engine.tf      # VM instance configuration
├── datastream.tf          # Datastream private connection
├── iam.tf                 # IAM roles and service accounts
├── provider.tf            # Google Cloud provider configuration
├── terraform.tfvars       # Variable values
└── variables.tf           # Variable definitions
```

## Resources Created

### Compute Engine
- 1 VM instance (`vm-datalake-endur`)
- 1 service account (`vm-default`)

### Datastream
- 1 private connection (`ex-datalake-ds-connection`)

### IAM
- Service account for VM with logging/monitoring roles
- Datastream service account with Secret Manager access
- Data lake users with comprehensive permissions.

### APIs Enabled
- BigQuery API
- Datastream API
- Cloud Functions API
- Dataplex API
- Compute Engine API
- BigQuery Data Transfer API
- Cloud Logging API
- Cloud Monitoring API
- Secret Manager API

## Security Considerations

- All resources are deployed in a shared VPC for network isolation
- Service accounts follow the principle of least privilege
- Datastream uses private connectivity for secure data transfer
- IAM roles are assigned based on job functions

## Maintenance

### Adding New Users
To add new data lake users:
1. Update the `datalake_users` variable in `variables.tf`
2. Run `terraform plan` and `terraform apply`


### State Management
- Terraform state is stored in GCS bucket: `ext-datalake-tf-state`
- State file prefix: `extra-dwh-terraform`

## Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure your GCP account has necessary IAM permissions
2. **Network Not Found**: Verify that the VPC and subnet exist in the specified project
3. **API Not Enabled**: Check that required APIs are enabled in the target project


## Contributing

1. Create a feature branch
2. Make your changes
3. Test with `terraform plan`
4. Submit a pull request
5. Ensure all checks pass before merging

---

**Note**: This infrastructure is designed for production use. Always review changes carefully before applying to avoid service disruptions.
