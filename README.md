# Extra Stores Data Warehouse - Terraform Infrastructure

Terraform configuration for Extra Stores data warehouse on Google Cloud Platform.

## Overview

Creates a complete data lake infrastructure with:
- Compute Engine VM for data processing
- Datastream private connection for real-time replication
- IAM roles for 7 data lake users
- Required GCP APIs and service accounts

## Quick Start

```bash
terraform init
terraform plan
terraform apply
```

## Configuration

| Variable | Value | Description |
|----------|-------|-------------|
| `project` | `ex-datalake-prod-prod` | GCP project ID |
| `region` | `me-central2` | Deployment region |
| `datalake_users` | 7 users | Data lake access users |

## Terraform Code Walkthrough

### File Structure

```
├── backend.tf            # Remote state in GCS
├── provider.tf           # Google Cloud provider
├── variables.tf          # Variable definitions
├── terraform.tfvars      # Variable values
├── api.tf               # Enable GCP APIs
├── iam.tf               # Service accounts & permissions
├── compute-engine.tf    # VM instance
└── datastream.tf        # Private connection
```

### 1. State Management (`backend.tf`)

<augment_code_snippet path="backend.tf" mode="EXCERPT">
````hcl
terraform {
  backend "gcs" {
    bucket = "ext-datalake-tf-state"
    prefix = "extra-dwh-terraform"
  }
}
````
</augment_code_snippet>

Stores Terraform state in GCS bucket for team collaboration and state locking.

### 2. Provider (`provider.tf`)

<augment_code_snippet path="provider.tf" mode="EXCERPT">
````hcl
provider "google" {
  add_terraform_attribution_label = false
}
````
</augment_code_snippet>

Configures Google Cloud provider without attribution labels.

### 3. Variables (`variables.tf`)

<augment_code_snippet path="variables.tf" mode="EXCERPT">
````hcl
variable "project" {
  type        = string
  description = "The GCP project ID"
}

variable "datalake_users" {
  type = list(string)
  default = [
    "user:<EMAIL>",
    "user:<EMAIL>",
    # ... 5 more users
  ]
}
````
</augment_code_snippet>

Defines input variables:
- `project` & `region`: Required GCP configuration
- `datalake_users`: List of 7 users with default values

### 4. Variable Values (`terraform.tfvars`)

<augment_code_snippet path="terraform.tfvars" mode="EXCERPT">
````hcl
project = "ex-datalake-prod-prod"
region  = "me-central2"
````
</augment_code_snippet>

Sets production environment values.

### 5. API Enablement (`api.tf`)

<augment_code_snippet path="api.tf" mode="EXCERPT">
````hcl
resource "google_project_service" "enable_apis" {
  for_each = toset([
    "bigquery.googleapis.com",
    "datastream.googleapis.com",
    "compute.googleapis.com",
    # ... 6 more APIs
  ])

  service = each.value
  disable_on_destroy = false
}
````
</augment_code_snippet>

Enables 9 GCP APIs using `for_each` loop. APIs remain enabled after destroy to prevent service disruption.

### 6. IAM Configuration (`iam.tf`)

Creates three IAM components using Google Terraform modules:

**A. VM Service Account**
<augment_code_snippet path="iam.tf" mode="EXCERPT">
````hcl
module "vm_default_sa" {
  source = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  name   = "vm-default"
}
````
</augment_code_snippet>

**B. VM Permissions**
- `roles/logging.logWriter` - Write logs
- `roles/monitoring.metricWriter` - Send metrics

**C. User Permissions**
Grants 13 admin roles to all `datalake_users`:
- BigQuery, Datastream, Storage, Secret Manager
- Compute, Cloud Functions, Dataplex, Composer
- Logging/Monitoring viewers, IAM, IAP access

### 7. Compute Engine (`compute-engine.tf`)

<augment_code_snippet path="compute-engine.tf" mode="EXCERPT">
````hcl
resource "google_compute_instance" "gce_vm" {
  name         = "vm-datalake-endur"
  machine_type = "n2-standard-4"
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = "debian-cloud/debian-12"
      size  = 30
      type  = "pd-balanced"
    }
  }

  service_account {
    email  = module.vm_default_sa.email
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
}
````
</augment_code_snippet>

Creates VM with:
- **Specs**: n2-standard-4 (4 vCPUs, 16 GB RAM)
- **OS**: Debian 12, 30 GB balanced disk
- **Network**: Shared VPC in `ex-network-nw` project
- **Security**: Uses `vm-default` service account

### 8. Datastream (`datastream.tf`)

<augment_code_snippet path="datastream.tf" mode="EXCERPT">
````hcl
resource "google_datastream_private_connection" "datastream_connection" {
  display_name          = "ex-datalake-ds-connection"
  private_connection_id = "datastream-connection"

  psc_interface_config {
    network_attachment = data.google_compute_network_attachment.datalake_network_attachment.id
  }
}

data "google_compute_network_attachment" "datalake_network_attachment" {
  name = "ex-network-attachment"
}
````
</augment_code_snippet>

Creates private connection for secure data replication using existing network attachment.

## Resource Dependencies

```
Variables/Provider → APIs → Service Accounts → VM & Datastream
                          ↘ IAM Bindings
```

1. **APIs** enabled first (prerequisite for all resources)
2. **Service accounts** created (needed for VM and IAM bindings)
3. **VM** uses service account from step 2
4. **Datastream** uses existing network attachment
5. **IAM bindings** grant permissions to users and service accounts

## Resources Created

- 1 VM instance (`vm-datalake-endur`)
- 1 service account (`vm-default`)
- 1 Datastream private connection
- 9 enabled GCP APIs
- IAM permissions for 7 users

## Maintenance

**Add Users**: Update `datalake_users` in `variables.tf`, run `terraform apply`

**State**: Stored in GCS bucket `ext-datalake-tf-state`