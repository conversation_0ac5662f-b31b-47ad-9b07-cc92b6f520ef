# Extra Stores Data Warehouse Terraform Infrastructure

This repository contains Terraform configuration for provisioning and managing the data warehouse infrastructure for Extra Stores on Google Cloud Platform (GCP).

## Overview

This Terraform configuration sets up a complete data lake and analytics infrastructure including:

- **Compute Engine VM** for data processing workloads
- **Datastream** for real-time data replication
- **BigQuery** for data warehousing and analytics
- **IAM roles and permissions** for data lake users
- **Required GCP APIs** enablement
- **Service accounts** with appropriate permissions

## Architecture

The infrastructure includes the following components:

### Compute Resources
- **VM Instance**: `vm-datalake-endur` (n2-standard-4) running Debian 12
- **Network**: Connected to shared VPC (`ex-datalake-prod-prod-vpc`)
- **Service Account**: Custom service account with logging and monitoring permissions

### Data Services
- **Datastream**: Private connection for real-time data replication
- **BigQuery**: Data warehouse for analytics (managed via IAM permissions)
- **Cloud Storage**: Object storage for data lake (managed via IAM permissions)

### Security & Access
- **IAM Bindings**: Comprehensive role assignments for data lake users
- **Service Accounts**: Dedicated service accounts for different services
- **Secret Manager**: For secure credential storage

## Prerequisites

Before using this Terraform configuration, ensure you have:

1. **Terraform** installed (version 0.12+)
2. **Google Cloud SDK** installed and configured
3. **GCP Project** with billing enabled
4. **Terraform state bucket** (`ext-datalake-tf-state`) already created
5. **Network infrastructure** (VPC and subnets) already provisioned
6. **Appropriate GCP permissions** to create resources

## Configuration

### Variables

The configuration uses the following variables:

| Variable | Type | Description | Default |
|----------|------|-------------|---------|
| `project` | string | The GCP project ID | - |
| `region` | string | The GCP region | - |
| `datalake_users` | list(string) | List of users with data lake access | See variables.tf |

### Default Values

Current configuration is set for:
- **Project**: `ex-datalake-prod-prod`
- **Region**: `me-central2`
- **Data Lake Users**: 7 users from extrastores.com domain

## Usage

### 1. Clone the Repository
```bash
git clone <repository-url>
cd extra-dwh-terraform
```

### 2. Initialize Terraform
```bash
terraform init
```

### 3. Review the Plan
```bash
terraform plan
```

### 4. Apply the Configuration
```bash
terraform apply
```

### 5. Verify Resources
After successful deployment, verify that all resources are created in the GCP Console.

## File Structure

```
.
├── README.md              # This file
├── api.tf                 # GCP API enablement
├── backend.tf             # Terraform backend configuration
├── compute-engine.tf      # VM instance configuration
├── datastream.tf          # Datastream private connection
├── iam.tf                 # IAM roles and service accounts
├── provider.tf            # Google Cloud provider configuration
├── terraform.tfvars       # Variable values
└── variables.tf           # Variable definitions
```

## Terraform Code Walkthrough

This section provides a detailed explanation of each Terraform file and how they work together to create the data warehouse infrastructure.

### 1. `variables.tf` - Variable Definitions

This file defines the input variables used throughout the configuration:

```hcl
variable "project" {
  type        = string
  description = "The GCP project ID"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "datalake_users" {
  type = list(string)
  default = [
    "user:<EMAIL>",
    "user:<EMAIL>",
    # ... more users
  ]
}
```

**Key Points:**
- `project` and `region` are required variables with no defaults
- `datalake_users` has a default list of 7 users from the extrastores.com domain
- These variables are referenced throughout other files using `var.variable_name`

### 2. `terraform.tfvars` - Variable Values

This file provides the actual values for the variables:

```hcl
project = "ex-datalake-prod-prod"
region  = "me-central2"
```

**Key Points:**
- Sets the target GCP project to `ex-datalake-prod-prod`
- Deploys resources in the `me-central2` region (Middle East - Central 2)
- The `datalake_users` variable uses its default value from `variables.tf`

### 3. `provider.tf` - Google Cloud Provider

Configures the Google Cloud provider:

```hcl
provider "google" {
  add_terraform_attribution_label = false
}
```

**Key Points:**
- Uses the default Google Cloud provider
- Disables Terraform attribution labels on resources
- Authentication is handled via environment variables or gcloud CLI

### 4. `backend.tf` - State Management

Configures remote state storage:

```hcl
terraform {
  backend "gcs" {
    bucket = "ext-datalake-tf-state"
    prefix = "extra-dwh-terraform"
  }
}
```

**Key Points:**
- Stores Terraform state in Google Cloud Storage
- Uses bucket `ext-datalake-tf-state` (must exist before running terraform)
- State file path: `extra-dwh-terraform/default.tfstate`
- Enables team collaboration and state locking

### 5. `api.tf` - GCP API Enablement

Enables required Google Cloud APIs:

```hcl
resource "google_project_service" "enable_apis" {
  project = var.project
  for_each = toset([
    "bigquery.googleapis.com",
    "datastream.googleapis.com",
    "cloudfunctions.googleapis.com",
    # ... more APIs
  ])

  service                    = each.value
  disable_on_destroy         = false
  disable_dependent_services = false
}
```

**Key Points:**
- Uses `for_each` to enable multiple APIs efficiently
- `disable_on_destroy = false` prevents API disabling when destroying resources
- Essential APIs for data warehouse functionality are enabled
- Must run before other resources that depend on these APIs

### 6. `iam.tf` - Identity and Access Management

This file contains three main IAM configurations:

#### A. VM Service Account Module
```hcl
module "vm_default_sa" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "vm-default"
  display_name = "vm-default"
}
```

**Purpose:** Creates a dedicated service account for the Compute Engine VM.

#### B. VM Service Account Permissions
```hcl
module "vm_default_sa_project_roles" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]
  bindings = {
    "roles/logging.logWriter"       = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
    "roles/monitoring.metricWriter" = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
  }
}
```

**Purpose:** Grants the VM service account permissions to write logs and metrics.

#### C. Datastream Service Account Permissions
```hcl
module "sa_datastream_secretmanager_secretaccessor" {
  # Grants Secret Manager access to Datastream service account
}
```

**Purpose:** Allows Datastream to access secrets for database connections.

#### D. Data Lake User Permissions
```hcl
module "datalake_project_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]
  bindings = {
    "roles/bigquery.admin"             = var.datalake_users
    "roles/datastream.admin"           = var.datalake_users
    "roles/storage.admin"              = var.datalake_users
    # ... more roles
  }
}
```

**Purpose:** Grants comprehensive permissions to data lake users for all data services.

**Key Points:**
- Uses Google's official Terraform modules for best practices
- Follows principle of least privilege for service accounts
- Grants broad permissions to data lake users for operational flexibility
- Service account email format: `{name}@{project}.iam.gserviceaccount.com`

### 7. `compute-engine.tf` - Virtual Machine Configuration

Creates a Compute Engine VM for data processing workloads:

```hcl
resource "google_compute_instance" "gce_vm" {
  name         = "vm-datalake-endur"
  project      = var.project
  machine_type = "n2-standard-4"
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = "debian-cloud/debian-12"
      size  = 30
      type  = "pd-balanced"
    }
  }

  network_interface {
    network    = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/global/networks/ex-datalake-prod-prod-vpc"
    subnetwork = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/regions/me-central2/subnetworks/ex-datalake-prod-prod-subnet"
  }

  service_account {
    email  = module.vm_default_sa.email
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
}
```

**Key Points:**
- **Machine Type:** n2-standard-4 (4 vCPUs, 16 GB RAM) suitable for data processing
- **Operating System:** Debian 12 (latest stable)
- **Disk:** 30 GB balanced persistent disk for cost-performance balance
- **Zone:** Automatically uses zone 'a' in the specified region
- **Network:** Connected to shared VPC in `ex-network-nw` project
- **Service Account:** Uses the VM service account created in `iam.tf`
- **Scopes:** Full cloud platform access for flexibility

### 8. `datastream.tf` - Real-time Data Replication

Sets up Datastream for real-time data replication:

```hcl
resource "google_datastream_private_connection" "datastream_connection" {
  display_name          = "ex-datalake-ds-connection"
  location              = var.region
  private_connection_id = "datastream-connection"
  project               = var.project

  labels = {
    companyname = "extra"
  }

  psc_interface_config {
    network_attachment = data.google_compute_network_attachment.datalake_network_attachment.id
  }
}

data "google_compute_network_attachment" "datalake_network_attachment" {
  project = var.project
  name    = "ex-network-attachment"
  region  = var.region
}
```

**Key Points:**
- **Private Connection:** Ensures secure, private connectivity for data replication
- **Network Attachment:** References existing network attachment for VPC connectivity
- **Data Source:** Uses `data` block to reference existing network infrastructure
- **Labels:** Includes company identification for resource management
- **Location:** Deployed in the same region as other resources for optimal performance

## How the Components Work Together

1. **Initialization:** `backend.tf` and `provider.tf` set up Terraform and GCP connectivity
2. **Variables:** `variables.tf` and `terraform.tfvars` define configuration parameters
3. **APIs:** `api.tf` enables required GCP services before creating resources
4. **Security:** `iam.tf` creates service accounts and assigns permissions
5. **Compute:** `compute-engine.tf` creates the VM using the service account from step 4
6. **Data Services:** `datastream.tf` sets up data replication infrastructure
7. **Dependencies:** Terraform automatically handles resource creation order based on references

## Resources Created

### Compute Engine
- 1 VM instance (`vm-datalake-endur`)
- 1 service account (`vm-default`)

### Datastream
- 1 private connection (`ex-datalake-ds-connection`)

### IAM
- Service account for VM with logging/monitoring roles
- Datastream service account with Secret Manager access
- Data lake users with comprehensive permissions.

### APIs Enabled
- BigQuery API
- Datastream API
- Cloud Functions API
- Dataplex API
- Compute Engine API
- BigQuery Data Transfer API
- Cloud Logging API
- Cloud Monitoring API
- Secret Manager API

## Security Considerations

- All resources are deployed in a shared VPC for network isolation
- Service accounts follow the principle of least privilege
- Datastream uses private connectivity for secure data transfer
- IAM roles are assigned based on job functions

## Maintenance

### Adding New Users
To add new data lake users:
1. Update the `datalake_users` variable in `variables.tf`
2. Run `terraform plan` and `terraform apply`


### State Management
- Terraform state is stored in GCS bucket: `ext-datalake-tf-state`
- State file prefix: `extra-dwh-terraform`

## Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure your GCP account has necessary IAM permissions
2. **Network Not Found**: Verify that the VPC and subnet exist in the specified project
3. **API Not Enabled**: Check that required APIs are enabled in the target project


## Contributing

1. Create a feature branch
2. Make your changes
3. Test with `terraform plan`
4. Submit a pull request
5. Ensure all checks pass before merging

---

**Note**: This infrastructure is designed for production use. Always review changes carefully before applying to avoid service disruptions.
