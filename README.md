# Extra Stores Data Warehouse - Terraform Infrastructure

Terraform configuration for data warehouse infrastructure on Google Cloud Platform.

## Repository Structure

```
├── backend.tf            # Remote state storage
├── provider.tf           # Google Cloud provider
├── variables.tf          # Input variables
├── terraform.tfvars      # Variable values
├── api.tf               # GCP API enablement
├── iam.tf               # Service accounts & permissions
├── compute-engine.tf    # VM instance
└── datastream.tf        # Private connection
```

## Configured Services

- **Compute Engine**: VM instance (`vm-datalake-endur`)
- **Datastream**: Private connection for data replication
- **IAM**: Service accounts and user permissions
- **APIs**: BigQuery, Datastream, Compute, Storage, etc.

## Configuration

| Variable | Value |
|----------|-------|
| `project` | `ex-datalake-prod-prod` |
| `region` | `me-central2` |
| `datalake_users` | 7 users |

## Usage

### Prerequisites
You must have the following roles on the `EX-Datalake-CMN-IAC` project:
- `roles/iam.serviceAccountUser`
- `roles/iam.serviceAccountTokenCreator`

### Service Account Impersonation
```bash
# Set service account impersonation
gcloud config set auth/impersonate_service_account=<EMAIL>

# Configure application default credentials
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

### Deploy Infrastructure
```bash
terraform init
terraform plan
terraform apply
```

