# Extra Stores Data Warehouse Infrastructure

This repository contains Terraform Infrastructure as Code (IaC) for provisioning and managing Extra Stores' data warehouse infrastructure on Google Cloud Platform (GCP). The configuration creates a complete data lake environment with compute resources, data streaming capabilities, and comprehensive access management.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Infrastructure Components](#infrastructure-components)
- [Prerequisites](#prerequisites)
- [Configuration](#configuration)
- [Deployment](#deployment)
- [File Structure](#file-structure)
- [Detailed Code Walkthrough](#detailed-code-walkthrough)
- [Resource Dependencies](#resource-dependencies)
- [Security & Access Control](#security--access-control)
- [Monitoring & Logging](#monitoring--logging)
- [Maintenance & Operations](#maintenance--operations)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## Architecture Overview

The infrastructure implements a modern data lake architecture on GCP with the following key characteristics:

- **Hybrid Connectivity**: Secure private connections for data ingestion
- **Scalable Compute**: Dedicated VM for data processing workloads
- **Real-time Streaming**: Datastream for continuous data replication
- **Centralized Access**: Comprehensive IAM management for data teams
- **Production-Ready**: Remote state management and API governance

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│   Datastream     │───▶│   BigQuery      │
│                 │    │  (Private Conn)  │    │  Data Warehouse │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Compute Engine  │
                       │  (Data Processing)│
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Cloud Storage   │
                       │   (Data Lake)    │
                       └──────────────────┘
```

## Infrastructure Components

### Core Services
- **Compute Engine VM**: `vm-datalake-endur` (n2-standard-4) for data processing and ETL workloads
- **Datastream**: Private connection for real-time database replication
- **BigQuery**: Data warehouse for analytics (access managed via IAM)
- **Cloud Storage**: Object storage for data lake (access managed via IAM)
- **Secret Manager**: Secure credential storage for database connections

### Networking
- **VPC Integration**: Connected to shared VPC `ex-datalake-prod-prod-vpc` in `ex-network-nw` project
- **Private Connectivity**: Datastream uses private service connect for secure data transfer
- **Network Attachment**: Leverages existing `ex-network-attachment` for VPC connectivity

### Security & Identity
- **Service Accounts**: Dedicated accounts for VM and Datastream operations
- **IAM Roles**: Comprehensive role assignments for 7 data lake users
- **Principle of Least Privilege**: Service accounts have minimal required permissions
- **Audit Logging**: Integrated with Cloud Logging and Monitoring

## Prerequisites

Before deploying this infrastructure, ensure the following requirements are met:

### Software Requirements
- **Terraform**: Version 0.12 or higher
- **Google Cloud SDK**: Latest version, authenticated with appropriate permissions
- **Git**: For version control and collaboration

### GCP Prerequisites
- **GCP Project**: `ex-datalake-prod-prod` with billing enabled
- **APIs Enabled**: Will be automatically enabled by this configuration
- **Network Infrastructure**:
  - VPC `ex-datalake-prod-prod-vpc` in project `ex-network-nw`
  - Subnet `ex-datalake-prod-prod-subnet` in region `me-central2`
  - Network attachment `ex-network-attachment`
- **State Storage**: GCS bucket `ext-datalake-tf-state` must exist

### Permissions Required
The deploying user/service account needs the following IAM roles:
- `roles/editor` or equivalent granular permissions
- `roles/iam.serviceAccountAdmin`
- `roles/resourcemanager.projectIamAdmin`
- `roles/serviceusage.serviceUsageAdmin`

## Configuration

### Environment Variables
The infrastructure is configured for the production environment:

| Variable | Value | Description | Required |
|----------|-------|-------------|----------|
| `project` | `ex-datalake-prod-prod` | Target GCP project ID | Yes |
| `region` | `me-central2` | Primary deployment region | Yes |
| `datalake_users` | 7 predefined users | List of users with data lake access | No (has defaults) |

### Data Lake Users
The following users have comprehensive admin access to all data services:
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

### Customization
To modify the configuration:
1. Update variables in `terraform.tfvars` for environment-specific values
2. Modify `variables.tf` to add new variables or change defaults
3. Adjust resource configurations in respective `.tf` files

## Deployment

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd extra-dwh-terraform

# Initialize Terraform
terraform init

# Review the deployment plan
terraform plan

# Deploy the infrastructure
terraform apply
```

### Detailed Deployment Steps

1. **Initialize Terraform Backend**
   ```bash
   terraform init
   ```
   This command:
   - Downloads required provider plugins
   - Configures the GCS backend for state storage
   - Prepares the working directory

2. **Validate Configuration**
   ```bash
   terraform validate
   terraform fmt -check
   ```

3. **Plan Deployment**
   ```bash
   terraform plan -out=tfplan
   ```
   Review the plan carefully to understand what resources will be created.

4. **Apply Configuration**
   ```bash
   terraform apply tfplan
   ```

5. **Verify Deployment**
   - Check GCP Console for created resources
   - Verify VM is running and accessible
   - Confirm Datastream connection is active
   - Test user permissions

### Deployment Considerations
- **Deployment Time**: Initial deployment takes approximately 10-15 minutes
- **Dependencies**: Resources are created in the correct order automatically
- **State Locking**: GCS backend provides state locking for team collaboration
- **Rollback**: Use `terraform destroy` for complete cleanup if needed

## File Structure

```
extra-dwh-terraform/
├── README.md              # This documentation
├── .gitignore            # Git ignore patterns
├── backend.tf            # Terraform backend configuration
├── provider.tf           # Google Cloud provider settings
├── variables.tf          # Input variable definitions
├── terraform.tfvars      # Variable values (environment-specific)
├── api.tf               # GCP API enablement
├── iam.tf               # Identity and Access Management
├── compute-engine.tf    # Virtual Machine configuration
└── datastream.tf        # Data streaming infrastructure
```

### File Purposes

| File | Purpose | Key Resources |
|------|---------|---------------|
| `backend.tf` | Remote state storage configuration | GCS backend |
| `provider.tf` | Google Cloud provider setup | Provider configuration |
| `variables.tf` | Input variable definitions | Variable declarations |
| `terraform.tfvars` | Environment-specific values | Variable assignments |
| `api.tf` | GCP service enablement | 9 Google APIs |
| `iam.tf` | Access control and permissions | Service accounts, IAM bindings |
| `compute-engine.tf` | Virtual machine infrastructure | Compute Engine instance |
| `datastream.tf` | Data replication services | Datastream private connection |

## Detailed Code Walkthrough

### 1. Backend Configuration (`backend.tf`)

<augment_code_snippet path="backend.tf" mode="EXCERPT">
````hcl
terraform {
  backend "gcs" {
    bucket = "ext-datalake-tf-state"
    prefix = "extra-dwh-terraform"
  }
}
````
</augment_code_snippet>

**Purpose**: Configures remote state storage in Google Cloud Storage
- **State File Location**: `gs://ext-datalake-tf-state/extra-dwh-terraform/default.tfstate`
- **Benefits**: Enables team collaboration, state locking, and disaster recovery
- **Security**: State file contains sensitive information and should be access-controlled

### 2. Provider Configuration (`provider.tf`)

<augment_code_snippet path="provider.tf" mode="EXCERPT">
````hcl
provider "google" {
  add_terraform_attribution_label = false
}
````
</augment_code_snippet>

**Purpose**: Configures the Google Cloud Terraform provider
- **Authentication**: Uses default application credentials or service account
- **Attribution Labels**: Disabled to avoid adding Terraform labels to resources
- **Implicit Configuration**: Project and region are specified via variables in resources

### 3. Variable Definitions (`variables.tf`)

<augment_code_snippet path="variables.tf" mode="EXCERPT">
````hcl
variable "project" {
  type        = string
  description = "The GCP project ID"
}

variable "datalake_users" {
  type = list(string)
  default = [
    "user:<EMAIL>",
    # ... more users
  ]
}
````
</augment_code_snippet>

**Key Variables**:
- `project`: Target GCP project (required)
- `region`: Deployment region (required)
- `datalake_users`: List of users with data lake access (has defaults)

**Design Patterns**:
- Required variables have no default values
- User list is centrally managed for easy updates
- Type constraints ensure data integrity

### 4. Variable Values (`terraform.tfvars`)

<augment_code_snippet path="terraform.tfvars" mode="EXCERPT">
````hcl
project = "ex-datalake-prod-prod"
region  = "me-central2"
````
</augment_code_snippet>

**Environment Configuration**:
- **Project**: Production data lake project
- **Region**: Middle East Central 2 (Dubai) for regional compliance
- **Users**: Uses default list from `variables.tf`

### 5. API Enablement (`api.tf`)

<augment_code_snippet path="api.tf" mode="EXCERPT">
````hcl
resource "google_project_service" "enable_apis" {
  project = var.project
  for_each = toset([
    "bigquery.googleapis.com",
    "datastream.googleapis.com",
    # ... more APIs
  ])

  service                    = each.value
  disable_on_destroy         = false
}
````
</augment_code_snippet>

**Enabled APIs**:
- BigQuery, Datastream, Cloud Functions, Dataplex
- Compute Engine, BigQuery Data Transfer
- Logging, Monitoring, Secret Manager

**Key Features**:
- `for_each` loop for efficient API management
- `disable_on_destroy = false` prevents service disruption
- Prerequisite for all other resources

### 6. Identity and Access Management (`iam.tf`)

The IAM configuration uses Google's official Terraform modules for best practices and consists of three main components:

#### A. VM Service Account Creation

<augment_code_snippet path="iam.tf" mode="EXCERPT">
````hcl
module "vm_default_sa" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "vm-default"
  display_name = "vm-default"
}
````
</augment_code_snippet>

**Purpose**: Creates a dedicated service account for the Compute Engine VM
- **Module**: Uses Google's official service account module
- **Naming**: `vm-default@{project}.iam.gserviceaccount.com`
- **Scope**: Project-level service account

#### B. VM Service Account Permissions

<augment_code_snippet path="iam.tf" mode="EXCERPT">
````hcl
module "vm_default_sa_project_roles" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"

  bindings = {
    "roles/logging.logWriter"       = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
    "roles/monitoring.metricWriter" = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
  }
}
````
</augment_code_snippet>

**Permissions Granted**:
- **Logging**: Write application and system logs
- **Monitoring**: Send custom metrics and monitoring data
- **Principle**: Minimal permissions for VM operations

#### C. Data Lake User Permissions

<augment_code_snippet path="iam.tf" mode="EXCERPT">
````hcl
module "datalake_project_iam_bindings" {
  bindings = {
    "roles/bigquery.admin"             = var.datalake_users
    "roles/datastream.admin"           = var.datalake_users
    "roles/storage.admin"              = var.datalake_users
    # ... 10 more admin roles
  }
}
````
</augment_code_snippet>

**Complete Role List**:
- `bigquery.admin` - Full BigQuery access
- `datastream.admin` - Manage Datastream resources
- `storage.admin` - Cloud Storage management
- `secretmanager.admin` - Secret management
- `compute.admin` - VM and compute resources
- `cloudfunctions.admin` - Serverless functions
- `dataplex.admin` - Data governance
- `composer.admin` - Workflow orchestration
- `logging.viewer` - View logs and audit trails
- `monitoring.viewer` - Access monitoring dashboards
- `iam.serviceAccountUser` - Use service accounts
- `storage.objectAdmin` - Object-level storage access
- `iap.tunnelResourceAccessor` - Secure tunnel access

**Design Rationale**:
- **Broad Permissions**: Data teams need comprehensive access for analytics
- **Centralized Management**: All users get identical permissions
- **Module-Based**: Leverages Google's best practices

### 7. Compute Engine Configuration (`compute-engine.tf`)

<augment_code_snippet path="compute-engine.tf" mode="EXCERPT">
````hcl
resource "google_compute_instance" "gce_vm" {
  name         = "vm-datalake-endur"
  machine_type = "n2-standard-4"
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = "debian-cloud/debian-12"
      size  = 30
      type  = "pd-balanced"
    }
  }
````
</augment_code_snippet>

**VM Specifications**:
- **Name**: `vm-datalake-endur` (descriptive naming convention)
- **Machine Type**: n2-standard-4 (4 vCPUs, 16 GB RAM)
- **Zone**: Automatically uses zone 'a' in the specified region
- **Operating System**: Debian 12 (latest stable LTS)
- **Boot Disk**: 30 GB balanced persistent disk (cost-performance optimized)

**Network Configuration**:

<augment_code_snippet path="compute-engine.tf" mode="EXCERPT">
````hcl
network_interface {
  network    = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/global/networks/ex-datalake-prod-prod-vpc"
  subnetwork = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/regions/me-central2/subnetworks/ex-datalake-prod-prod-subnet"
}
````
</augment_code_snippet>

**Network Details**:
- **VPC**: Shared VPC in `ex-network-nw` project
- **Subnet**: Regional subnet in `me-central2`
- **Security**: No external IP (private instance)
- **Connectivity**: Access via IAP tunnel or VPN

**Service Account Integration**:

<augment_code_snippet path="compute-engine.tf" mode="EXCERPT">
````hcl
service_account {
  email  = module.vm_default_sa.email
  scopes = ["https://www.googleapis.com/auth/cloud-platform"]
}
````
</augment_code_snippet>

**Security Features**:
- **Service Account**: Uses dedicated `vm-default` service account
- **Scopes**: Full cloud platform access for flexibility
- **Authentication**: Automatic credential management

### 8. Datastream Configuration (`datastream.tf`)

<augment_code_snippet path="datastream.tf" mode="EXCERPT">
````hcl
resource "google_datastream_private_connection" "datastream_connection" {
  display_name          = "ex-datalake-ds-connection"
  location              = var.region
  private_connection_id = "datastream-connection"

  psc_interface_config {
    network_attachment = data.google_compute_network_attachment.datalake_network_attachment.id
  }
}
````
</augment_code_snippet>

**Connection Features**:
- **Private Connectivity**: Secure, private connection for data replication
- **Regional Deployment**: Same region as other resources for optimal performance
- **Network Integration**: Uses existing network attachment for VPC connectivity
- **Labeling**: Tagged with company identifier for resource management

**Network Attachment Reference**:

<augment_code_snippet path="datastream.tf" mode="EXCERPT">
````hcl
data "google_compute_network_attachment" "datalake_network_attachment" {
  project = var.project
  name    = "ex-network-attachment"
  region  = var.region
}
````
</augment_code_snippet>

**Infrastructure Dependencies**:
- **Data Source**: References existing network attachment
- **Prerequisite**: Network attachment must exist before deployment
- **Scope**: Project and region-specific resource

## Resource Dependencies

The Terraform configuration manages resource dependencies automatically, but understanding the dependency chain is crucial for troubleshooting and modifications:

```mermaid
graph TD
    A[Variables & Provider] --> B[API Enablement]
    B --> C[Service Accounts]
    B --> D[Network Data Sources]
    C --> E[VM Instance]
    D --> F[Datastream Connection]
    C --> G[IAM Bindings]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#fce4ec
```

### Dependency Levels

1. **Foundation Layer**
   - Variable definitions and provider configuration
   - No dependencies

2. **API Layer**
   - GCP service enablement (`api.tf`)
   - Depends on: Provider configuration
   - Required for: All GCP resources

3. **Identity Layer**
   - Service account creation (`iam.tf`)
   - Depends on: API enablement
   - Required for: VM instance, IAM bindings

4. **Network Layer**
   - Network attachment data source (`datastream.tf`)
   - Depends on: API enablement
   - Required for: Datastream connection

5. **Compute Layer**
   - VM instance creation (`compute-engine.tf`)
   - Depends on: Service accounts, API enablement
   - No dependents

6. **Data Layer**
   - Datastream private connection (`datastream.tf`)
   - Depends on: Network attachment, API enablement
   - No dependents

7. **Access Layer**
   - User IAM bindings (`iam.tf`)
   - Depends on: Service accounts, API enablement
   - No dependents

### Critical Dependencies

- **APIs First**: All GCP services must be enabled before creating resources
- **Service Accounts**: Must exist before being assigned to VM or IAM bindings
- **Network Prerequisites**: VPC and network attachment must exist externally
- **Regional Consistency**: All resources deployed in the same region

## Security & Access Control

### Security Architecture

The infrastructure implements a multi-layered security approach:

#### 1. Network Security
- **Private VM**: No external IP address, access via IAP or VPN only
- **Shared VPC**: Centralized network management and security policies
- **Private Service Connect**: Secure Datastream connectivity without internet exposure
- **Regional Isolation**: All resources within single region boundary

#### 2. Identity & Access Management
- **Service Account Isolation**: Dedicated service accounts with minimal permissions
- **User Role Segregation**: Data lake users have comprehensive but scoped access
- **Module-Based IAM**: Uses Google's official modules for security best practices
- **Audit Trail**: All actions logged via Cloud Audit Logs

#### 3. Data Protection
- **Encryption at Rest**: All storage encrypted with Google-managed keys
- **Encryption in Transit**: TLS for all data movement
- **Secret Management**: Database credentials stored in Secret Manager
- **Access Logging**: Comprehensive audit trail for data access

### Access Patterns

#### Data Lake Users
- **Scope**: Full administrative access to data services
- **Use Cases**: Data analysis, pipeline development, resource management
- **Access Method**: Google Cloud Console, CLI, APIs
- **Monitoring**: All actions logged and monitored

#### VM Service Account
- **Scope**: Minimal permissions for logging and monitoring
- **Use Cases**: Application logging, system metrics
- **Access Method**: Automatic via VM metadata service
- **Security**: No user impersonation capabilities

#### Datastream Service Account
- **Scope**: Secret Manager access for database connections
- **Use Cases**: Automated data replication
- **Access Method**: Automatic via Datastream service
- **Security**: Google-managed service account

## Monitoring & Logging

### Integrated Monitoring

The infrastructure includes comprehensive monitoring and logging capabilities:

#### Cloud Logging Integration
- **VM Logs**: Application and system logs from Compute Engine
- **Audit Logs**: All API calls and resource changes
- **Service Logs**: Datastream operation logs
- **Access Logs**: User activity and permission usage

#### Cloud Monitoring Integration
- **VM Metrics**: CPU, memory, disk, and network utilization
- **Custom Metrics**: Application-specific metrics via VM service account
- **Service Metrics**: Datastream throughput and latency
- **Alerting**: Can be configured for operational thresholds

#### Log Retention
- **Default Retention**: 30 days for most logs
- **Audit Logs**: Extended retention for compliance
- **Custom Retention**: Configurable per log type
- **Export Options**: BigQuery, Cloud Storage, or Pub/Sub

## Maintenance & Operations

### Regular Maintenance Tasks

#### User Management
```bash
# Add new user to data lake access
# 1. Edit variables.tf
vim variables.tf

# 2. Add user to datalake_users list
# 3. Apply changes
terraform plan
terraform apply
```

#### Resource Updates
```bash
# Update VM machine type
# 1. Edit compute-engine.tf
# 2. Plan and apply changes (may require VM restart)
terraform plan
terraform apply
```

#### State Management
```bash
# View current state
terraform show

# List all resources
terraform state list

# Import existing resource (if needed)
terraform import google_compute_instance.gce_vm projects/{project}/zones/{zone}/instances/{name}
```

### Backup and Recovery

#### State Backup
- **Automatic**: GCS backend provides versioning
- **Manual Backup**: `gsutil cp gs://ext-datalake-tf-state/extra-dwh-terraform/default.tfstate backup/`
- **Recovery**: Restore from GCS version history

#### Resource Recovery
- **VM Recovery**: Recreate from Terraform configuration
- **Data Recovery**: Depends on external backup strategies
- **Network Recovery**: Shared VPC managed externally

### Scaling Considerations

#### Vertical Scaling
- **VM Scaling**: Change machine type in `compute-engine.tf`
- **Disk Scaling**: Increase boot disk size (requires VM restart)
- **Memory Optimization**: Consider memory-optimized machine types for data processing

#### Horizontal Scaling
- **Multiple VMs**: Add additional VM resources
- **Load Balancing**: Implement if needed for distributed processing
- **Regional Expansion**: Deploy in additional regions

## Troubleshooting

### Common Issues and Solutions

#### 1. Terraform Initialization Failures
```bash
# Error: Backend configuration changed
# Solution: Reinitialize backend
terraform init -reconfigure

# Error: Provider version conflicts
# Solution: Upgrade providers
terraform init -upgrade
```

#### 2. Permission Denied Errors
```bash
# Error: Insufficient permissions
# Check: User has required IAM roles
gcloud projects get-iam-policy ex-datalake-prod-prod

# Solution: Add missing roles
gcloud projects add-iam-policy-binding ex-datalake-prod-prod \
  --member="user:<EMAIL>" \
  --role="roles/editor"
```

#### 3. Resource Creation Failures
```bash
# Error: API not enabled
# Solution: Enable required APIs manually
gcloud services enable compute.googleapis.com --project=ex-datalake-prod-prod

# Error: Network not found
# Verify: Network exists in correct project
gcloud compute networks list --project=ex-network-nw
```

#### 4. VM Access Issues
```bash
# Error: Cannot SSH to VM
# Solution: Use IAP tunnel
gcloud compute ssh vm-datalake-endur \
  --zone=me-central2-a \
  --project=ex-datalake-prod-prod \
  --tunnel-through-iap
```

#### 5. Datastream Connection Issues
```bash
# Error: Private connection failed
# Check: Network attachment exists
gcloud compute network-attachments describe ex-network-attachment \
  --region=me-central2 \
  --project=ex-datalake-prod-prod
```

### Debugging Commands

```bash
# View detailed resource information
terraform show

# Check resource dependencies
terraform graph | dot -Tpng > dependencies.png

# Validate configuration
terraform validate

# Format code
terraform fmt -recursive

# Check for security issues
terraform plan -out=tfplan
terraform show -json tfplan | jq '.'
```

## Best Practices

### Development Workflow

1. **Version Control**
   - Always commit changes to Git
   - Use meaningful commit messages
   - Create feature branches for major changes

2. **Testing**
   - Run `terraform plan` before applying
   - Test in development environment first
   - Validate with `terraform validate`

3. **Documentation**
   - Update README for configuration changes
   - Document custom modifications
   - Maintain change log

### Security Best Practices

1. **State File Security**
   - Restrict access to state bucket
   - Enable versioning and logging
   - Never commit state files to Git

2. **Credential Management**
   - Use service accounts for automation
   - Rotate credentials regularly
   - Avoid hardcoded secrets

3. **Access Control**
   - Follow principle of least privilege
   - Regular access reviews
   - Use groups for user management

### Operational Best Practices

1. **Monitoring**
   - Set up alerting for critical resources
   - Monitor costs and usage
   - Regular security audits

2. **Backup**
   - Regular state backups
   - Document recovery procedures
   - Test disaster recovery

3. **Updates**
   - Keep Terraform and providers updated
   - Regular security patches for VMs
   - Monitor for deprecated resources

---

## Support and Contributing

For questions, issues, or contributions:

1. **Internal Issues**: Contact the data engineering team
2. **Infrastructure Changes**: Create pull request with detailed description
3. **Emergency**: Follow incident response procedures
4. **Documentation**: Update README for any configuration changes

**Last Updated**: 2025-10-08
**Terraform Version**: >= 0.12
**Provider Version**: google >= 4.0