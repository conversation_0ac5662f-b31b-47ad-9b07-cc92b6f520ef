variable "project" {
  type        = string
  description = "The GCP project ID"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "datalake_users" {
  type = list(string)
  default = [
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>"

  ]
}
variable "gcpdataanalytics_users" {
  type = list(string)
  default = [
    "group:<EMAIL>"
  ]
}

