resource "google_project_service" "enable_apis" {
  project = var.project
  for_each = toset([
    "bigquery.googleapis.com",
    "datastream.googleapis.com",
    "cloudfunctions.googleapis.com",
    "dataplex.googleapis.com",
    "compute.googleapis.com",
    "bigquerydatatransfer.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "secretmanager.googleapis.com",
    "cloudshell.googleapis.com",
    "cloudscheduler.googleapis.com"
  ])

  service                    = each.value
  disable_on_destroy         = false
  disable_dependent_services = false
}