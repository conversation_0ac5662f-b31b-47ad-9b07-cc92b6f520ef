resource "google_compute_instance" "gce_vm" {
  name         = "vm-datalake-endur"
  project      = var.project
  machine_type = "n2-custom-2-5632"
  zone         = "${var.region}-a"



  boot_disk {
    initialize_params {

      image = "debian-cloud/debian-12"
      size  = 30
      type  = "pd-balanced"

    }

  }

  network_interface {

    network    = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/global/networks/ex-datalake-prod-prod-vpc"
    subnetwork = "https://www.googleapis.com/compute/v1/projects/ex-network-nw/regions/me-central2/subnetworks/ex-datalake-prod-prod-subnet"

  }

  service_account {
    email  = module.vm_default_sa.email
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
  lifecycle {
    ignore_changes = [metadata["ssh-keys"]]
  }
}







