module "vm_default_sa" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "vm-default"
  display_name = "vm-default"
}

module "vm_default_sa_project_roles" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/logging.logWriter"       = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
    "roles/monitoring.metricWriter" = ["serviceAccount:vm-default@${var.project}.iam.gserviceaccount.com"]
  }
}

module "sa_datastream_secretmanager_secretaccessor" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
      "roles/secretmanager.secretAccessor" = [
      "serviceAccount:<EMAIL>"
    ]
  }
}



module "datalake_project_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/bigquery.admin"             = var.datalake_users
    "roles/datastream.admin"           = var.datalake_users
    "roles/storage.admin"              = var.datalake_users
    "roles/secretmanager.admin"        = var.datalake_users
    "roles/storage.objectAdmin"        = var.datalake_users
    "roles/iam.serviceAccountUser"     = var.datalake_users
    "roles/compute.admin"              = var.datalake_users
    "roles/cloudfunctions.admin"       = var.datalake_users
    "roles/dataplex.admin"             = var.datalake_users
    "roles/composer.admin"             = var.datalake_users
    "roles/logging.viewer"             = var.datalake_users
    "roles/monitoring.viewer"          = var.datalake_users
    "roles/iap.tunnelResourceAccessor" = var.datalake_users
  }
}

