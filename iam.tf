module "vm_default_sa" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "vm-default"
  display_name = "vm-default"
}

module "vm_default_sa_project_roles" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/logging.logWriter"       = ["serviceAccount:${module.vm_default_sa.email}"]
    "roles/monitoring.metricWriter" = ["serviceAccount:${module.vm_default_sa.email}"]
    "roles/datastream.admin"        = ["serviceAccount:${module.vm_default_sa.email}"]
    "roles/storage.admin"           = ["serviceAccount:${module.vm_default_sa.email}"]
    "roles/bigquery.dataEditor"     = ["serviceAccount:${module.vm_default_sa.email}"]
    "roles/bigquery.jobUser"        = ["serviceAccount:${module.vm_default_sa.email}"]
  }
}

module "sa_datastream_secretmanager_secretaccessor" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/secretmanager.secretAccessor" = ["serviceAccount:<EMAIL>"]
  }
}

module "compute_default_sa_iam" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/logging.logWriter" = ["serviceAccount:<EMAIL>"]
    "roles/run.builder"       = ["serviceAccount:<EMAIL>"]
  }
}


module "datalake_project_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/bigquery.admin"                    = var.datalake_users
    "roles/datastream.admin"                  = var.datalake_users
    "roles/storage.admin"                     = var.datalake_users
    "roles/secretmanager.admin"               = var.datalake_users
    "roles/storage.objectAdmin"               = var.datalake_users
    "roles/iam.serviceAccountUser"            = var.datalake_users
    "roles/compute.admin"                     = var.datalake_users
    "roles/cloudfunctions.admin"              = var.datalake_users
    "roles/dataplex.admin"                    = var.datalake_users
    "roles/composer.admin"                    = var.datalake_users
    "roles/logging.viewer"                    = var.datalake_users
    "roles/monitoring.viewer"                 = var.datalake_users
    "roles/iap.tunnelResourceAccessor"        = var.datalake_users
    "roles/run.sourceDeveloper"               = var.datalake_users
    "roles/serviceusage.serviceUsageConsumer" = var.datalake_users
    "roles/cloudscheduler.admin"              = var.datalake_users
    "roles/dataflow.admin"                   = var.datalake_users
    "roles/datapipelines.admin"              = var.datalake_users
  }
}
module "_project_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {

    "roles/logging.viewer" = var.gcpdataanalytics_users

  }
}

module "cf_api_bq_loader_sa" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "cf-api-bq-loader-sa"
  display_name = "cf-api-bq-loader-sa"
}

module "cf_api_bq_loader_sa_project_roles" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 8.1"

  projects = [var.project]

  bindings = {
    "roles/run.builder"                  = ["serviceAccount:${module.cf_api_bq_loader_sa.email}"]
    "roles/run.invoker"                  = ["serviceAccount:${module.cf_api_bq_loader_sa.email}"]
    "roles/bigquery.dataEditor"          = ["serviceAccount:${module.cf_api_bq_loader_sa.email}"]
    "roles/bigquery.jobUser"             = ["serviceAccount:${module.cf_api_bq_loader_sa.email}"]
    "roles/secretmanager.secretAccessor" = ["serviceAccount:${module.cf_api_bq_loader_sa.email}"]
  }
}

## Dataflow POC 

module "cf_api_bq_loader_s" {
  source       = "terraform-google-modules/service-accounts/google//modules/simple-sa"
  version      = "~> 4.5"
  project_id   = var.project
  name         = "cf-api-bq-loader-sa"
  display_name = "cf-api-bq-loader-sa"
}