resource "google_datastream_private_connection" "datastream_connection" {
  display_name          = "ex-datalake-ds-connection"
  location              = var.region
  private_connection_id = "datastream-connection"
  project               = var.project

  labels = {
    companyname = "extra"
  }

  psc_interface_config {
    network_attachment = data.google_compute_network_attachment.datalake_network_attachment.id
  }
}

data "google_compute_network_attachment" "datalake_network_attachment" {
  project = var.project
  name    = "ex-network-attachment"
  region  = var.region
}
